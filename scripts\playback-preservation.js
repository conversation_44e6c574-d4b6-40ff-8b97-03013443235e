// Global Playback Preservation System
// This script ensures uninterrupted music playback across all page navigation and interactions

(function() {
  'use strict';

  // Global playback preservation manager
  window.PlaybackPreservation = {
    isInitialized: false,
    
    init: function() {
      if (this.isInitialized) return;
      
      console.log("Initializing Playback Preservation System");
      
      // Preserve playback on page unload
      window.addEventListener('beforeunload', this.preserveOnUnload.bind(this));
      
      // Preserve playback on page visibility change
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
      
      // Preserve playback on focus/blur
      window.addEventListener('blur', this.preserveOnBlur.bind(this));
      window.addEventListener('focus', this.restoreOnFocus.bind(this));
      
      // Override navigation methods to preserve playback
      this.overrideNavigation();
      
      this.isInitialized = true;
    },
    
    preserveOnUnload: function() {
      if (window.GlobalAudioManager) {
        const state = window.GlobalAudioManager.preservePlayback();
        console.log("Preserving playback on page unload:", state);
      }
    },
    
    handleVisibilityChange: function() {
      if (document.hidden) {
        // Page is hidden, preserve state
        if (window.GlobalAudioManager) {
          window.GlobalAudioManager.preservePlayback();
        }
      } else {
        // Page is visible again, ensure playback continues
        this.ensurePlaybackContinuity();
      }
    },
    
    preserveOnBlur: function() {
      if (window.GlobalAudioManager) {
        window.GlobalAudioManager.preservePlayback();
      }
    },
    
    restoreOnFocus: function() {
      this.ensurePlaybackContinuity();
    },
    
    ensurePlaybackContinuity: function() {
      if (!window.GlobalAudioManager) return;
      
      const music = window.GlobalAudioManager.getInstance();
      if (!music) return;
      
      // Check if music should be playing but isn't
      const state = getCurrentPlayerState ? getCurrentPlayerState() : null;
      if (state && state.isPlaying && music.paused) {
        // Try to resume playback
        music.play().then(() => {
          console.log("Resumed playback after focus/visibility change");
        }).catch((error) => {
          console.log("Could not resume playback:", error);
        });
      }
    },
    
    overrideNavigation: function() {
      // Override window.location.href assignments
      const originalLocationSetter = Object.getOwnPropertyDescriptor(window.location, 'href') || 
                                   Object.getOwnPropertyDescriptor(Location.prototype, 'href');
      
      if (originalLocationSetter && originalLocationSetter.set) {
        Object.defineProperty(window.location, 'href', {
          set: function(url) {
            // Preserve playback before navigation
            if (window.GlobalAudioManager) {
              window.GlobalAudioManager.preservePlayback();
            }
            originalLocationSetter.set.call(this, url);
          },
          get: originalLocationSetter.get
        });
      }
      
      // Override history navigation
      const originalPushState = history.pushState;
      const originalReplaceState = history.replaceState;
      
      history.pushState = function() {
        if (window.GlobalAudioManager) {
          window.GlobalAudioManager.preservePlayback();
        }
        return originalPushState.apply(this, arguments);
      };
      
      history.replaceState = function() {
        if (window.GlobalAudioManager) {
          window.GlobalAudioManager.preservePlayback();
        }
        return originalReplaceState.apply(this, arguments);
      };
      
      // Override link clicks
      document.addEventListener('click', function(e) {
        const link = e.target.closest('a[href]');
        if (link && !link.href.startsWith('javascript:') && !link.href.startsWith('#')) {
          // This is a navigation link, preserve playback
          if (window.GlobalAudioManager) {
            window.GlobalAudioManager.preservePlayback();
          }
        }
      }, true);
    },
    
    // Method to ensure audio element is not recreated
    protectAudioElement: function() {
      if (!window.GlobalAudioManager) return;
      
      const music = window.GlobalAudioManager.getInstance();
      if (!music) return;
      
      // Prevent audio element from being garbage collected
      window._protectedAudio = music;
      
      // Add error recovery
      music.addEventListener('error', function(e) {
        console.error('Audio error occurred:', e);
        // Try to recover by reloading the current source
        const currentSrc = music.src;
        if (currentSrc) {
          setTimeout(() => {
            music.src = currentSrc;
            music.load();
          }, 1000);
        }
      });
      
      // Add stalled event handler
      music.addEventListener('stalled', function() {
        console.log('Audio stalled, attempting recovery');
        music.load();
      });
    }
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      window.PlaybackPreservation.init();
      window.PlaybackPreservation.protectAudioElement();
    });
  } else {
    window.PlaybackPreservation.init();
    window.PlaybackPreservation.protectAudioElement();
  }

  // Also initialize immediately if GlobalAudioManager is available
  if (window.GlobalAudioManager) {
    window.PlaybackPreservation.protectAudioElement();
  }

})();

// Export for manual initialization if needed
window.initializePlaybackPreservation = function() {
  if (window.PlaybackPreservation) {
    window.PlaybackPreservation.init();
    window.PlaybackPreservation.protectAudioElement();
  }
};
